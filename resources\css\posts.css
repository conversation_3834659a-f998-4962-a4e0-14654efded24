.posts{
    background-color: #E8EFFF;
    max-width: 100vw;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.posts-container{
    width: 77%;
    align-self: center;
    flex: 1;
    display: grid;
    grid-template: 200px 1fr / 1fr 300px;
    column-gap: 1rem;
}

.posts-header-container {grid-area: 1 / 1 / 2 / 2;}

.posts-content-container {grid-area: 2 / 1 / 3 / 2;}

.posts-recent-container {grid-area: 2 / 2 / 3 / 3;}

.posts-header{
    display: grid;
    grid-template: 1fr 1fr / 1fr 1fr;
}

.posts-select{
    display: flex;
    flex-direction: column;
    gap: .1rem;
    max-width: 200px;
}

.posts-select select{
    border-radius: 8px;
    padding: 5px;
    width: 200px;
}

.posts-filter{
    grid-area: 1 / 1 / 2 / 2;
}

.posts-search{
    grid-area: 1 / 2;
}

.posts-sort{
    grid-area: 2 / 1;
}

.posts-add{
    grid-area: 2 / 2;
}

.posts-content,
.posts-recent{
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
}

.posts-recent-container{
    background-color: #D2E1F2;
    border-radius: 8px;
    padding: 20px 10px;
    height: 700px;
}

.posts-recent{
    border-radius: 8px;
    height: 130px;
    padding: 10px;
}

.post{
    text-decoration: none;
}

.post-dropdown:hover{
    cursor: pointer;
}

.post-dropdown ~ .dropdown-menu .dropdown-item:hover{
    color: white;
}

.category-container,
.category-type-container{
    border-radius: 10px;
    width: 100px;
    height: 30px;
    color: white;
}