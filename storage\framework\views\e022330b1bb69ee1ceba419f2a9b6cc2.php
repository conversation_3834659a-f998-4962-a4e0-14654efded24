<?php $__env->startSection('content'); ?>
    <div class="course pb-5">
        <?php echo $__env->make('shared.navbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <div class="d-flex flex-column align-items-center mt-3">
            <div class="course-logo">
                <img src="<?php echo e(asset('images/C_Programming_Language.png')); ?>" alt="C_Programming_Language" width="80"
                    height="95">
            </div>
            <div class="course-basic-syntax my-4 d-flex gap-3 justify-content-center align-items-center">
                <div class="course-line"></div>
                <h2 class="d-flex justify-content-center"><span>Basic Syntax</span></h2>
                <div class="course-line"></div>
            </div>

            <div class="d-flex flex-column text-center gap-5 w-100 align-items-center">
                <?php $__currentLoopData = $levelBS; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $level): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <a class="course-level-link" href="<?php echo e(route('level', ['course' => $level->course->id, 'level'=>$level->id])); ?>">
                        <div class="level" data-number="<?php echo e($level->number); ?>"><?php echo e($level->number); ?></div>
                    </a>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <div class="course-conditionals-loops my-4 d-flex gap-3 justify-content-center align-items-center">
                <div class="course-line"></div>
                <h2 class="d-flex justify-content-center"><span>Conditionals & Loops</span></h2>
                <div class="course-line"></div>
            </div>

            <div class="d-flex flex-column text-center gap-5 w-100 align-items-center">
                <?php $__currentLoopData = $levelCL; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $level): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <a class="course-level-link" href="<?php echo e(route('level', ['course' => $level->course->id, 'level'=>$level->id])); ?>">
                        <div class="level" data-number="<?php echo e($level->number); ?>"><?php echo e($level->number); ?></div>
                    </a>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            </div>

            <div class="course-functions my-4 d-flex gap-3 justify-content-center align-items-center">
                <div class="course-line"></div>
                <h2 class="d-flex justify-content-center"><span><?php echo e($course->course_name); ?> Functions</span></h2>
                <div class="course-line"></div>
            </div>

            <div class="d-flex flex-column text-center gap-5 w-100 align-items-center">
                <?php $__currentLoopData = $levelFN; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $level): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <a class="course-level-link" href="<?php echo e(route('level', ['course' => $level->course->id, 'level'=>$level->id])); ?>">
                        <div class="level" data-number="<?php echo e($level->number); ?>"><?php echo e($level->number); ?></div>
                    </a>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('shared.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\codinglinguist\resources\views/course.blade.php ENDPATH**/ ?>