.question-page{
    max-width: 100vw;
    min-height: 100vh;
    background: linear-gradient(to bottom, #D1C2FF, #ABD4FE);
    padding: 0 0 40px 0;
}

.level-question-nav{
    height: 150px;
    background-color: #E8EFFF;
    width: 75%;
    border-radius: 20px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
}

.level-question-nav .nav-item{
    background-color: #D1D1D1;
    border-radius: 15px;
    width: 60px;
    height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.5rem;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
}

.question-header{
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.question-content{
    font-weight: 400;
    align-self: flex-start;
    margin: 10px 0;
    width: 100%;
}

.question-sub-header{
    font-size: 1.5rem;
    font-weight: 600;
    align-self: flex-start;
    margin: 20px 0 10px 0;
}

.question-code-input{
    align-self: flex-start;
    width: 100%;
    padding: 20px;
    border-radius: 5px;
    background-color: #272822;
    color: white;
}

.question-list{
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}
.question-choice{
    list-style: none;
    border-radius: 5px;
    border: 1px solid black;
    padding: 1rem;
    width: 100%;
    cursor: pointer;
}
.question-input{
    border-radius: 5px;
    border: 1px solid black;
    padding: 1rem;
    width: 100%;
    background: none;
}