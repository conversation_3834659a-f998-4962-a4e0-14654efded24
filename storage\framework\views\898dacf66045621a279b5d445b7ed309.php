<?php $__env->startSection('content'); ?>
    <div class="level-page">
        <?php echo $__env->make('shared.navbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <div class="d-flex flex-column w-100 align-items-center">
            <div class="level-question-nav my-4">
                <a
                    href="<?php echo e(route('level', ['course' => $level->course->id, 'level' => $level->id])); ?>">
                    <div class="nav-item bg-primary text-white">
                        i
                    </div>
                </a>
                <?php $__currentLoopData = $level->questions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $question): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <a
                        href="<?php echo e(route('question', ['course' => $level->course->id, 'level' => $level->id, 'question' => $question->id])); ?>">
                        <div class="nav-item">
                            <?php echo e($question->number); ?>

                        </div>
                    </a>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <div class="level-container d-flex flex-column align-items-center">
                <?php echo $__env->make('level-content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('shared.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\codinglinguist\resources\views/level.blade.php ENDPATH**/ ?>