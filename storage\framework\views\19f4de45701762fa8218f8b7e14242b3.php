<?php $__env->startSection('content'); ?>
    <div class="question-page">
        <?php echo $__env->make('shared.navbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <div class="d-flex flex-column w-100 align-items-center">
            <div class="level-question-nav my-4">
                <a
                    href="<?php echo e(route('level', ['course' => $level->course->id, 'level' => $level->id])); ?>">
                    <div class="nav-item">
                        i
                    </div>
                </a>
                <?php $__currentLoopData = $level->questions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $qst): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <a
                        href="<?php echo e(route('question', ['course' => $level->course->id, 'level' => $level->id, 'question' => $qst->id])); ?>">
                        <div class="nav-item <?php echo e($question->id == $qst->id ? "bg-primary text-white" : ""); ?>">
                            <?php echo e($qst->number); ?>

                        </div>
                    </a>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <div class="level-container d-flex flex-column align-items-center">
                <?php echo $__env->make('question-content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('shared.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\codinglinguist\resources\views/question.blade.php ENDPATH**/ ?>