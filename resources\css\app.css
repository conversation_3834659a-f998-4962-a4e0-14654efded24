@import 'bootstrap';
@import 'bootstrap-icons';
@import url('https://fonts.googleapis.com/css2?family=K2D:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');
@import 'home';
@import 'user-dashboard';
@import 'sign-up';
@import 'sign-in';
@import 'create-post';
@import 'posts';
@import 'post-detail';
@import 'course';
@import 'level';
@import 'question';

/* Resets */
*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body{
    font-family: "K2D", sans-serif;
    font-weight: 400;
    font-style: normal;
}

p{
    margin: 0;
}

.form-control{
    border: none;
}

textarea{
    resize: none;
}

.card-title:hover{
    text-decoration: underline;
}

.card-link{
    text-decoration: none;
    color: black;
}

a{
    text-decoration: none;
    color: black;
}

/* Layouts */
.navbar{
    width: 80%;
}

.navbar-container{
    height: fit-content;
}

.dropdown-menu{
    background-color: #BFCBFF;
}

.dropdown-item:hover{
    background-color: #8d96bb;
}

/* Extra */

.btn-post{
    border: 1px solid #4992FD;
    color: #4992FD;
}

.btn-cancel{
    border: 1px solid #E04E5C;
    color: #E04E5C;
}

.btn-post:hover{
    background-color: #4992FD;
    color: white;
}

.btn-cancel:hover{
    background-color: #E04E5C;
    color: white;
}
